"""
HVI-CIDNet: 基于HVI颜色空间的低光图像增强网络

本模块实现了完整的HVI-CIDNet网络架构，这是一个专门为低光图像增强设计的深度学习模型。
HVI-CIDNet的核心创新在于：

1. HVI颜色空间：
   - 将RGB图像转换为HVI颜色空间进行处理
   - H(Hue)通道保持色调信息
   - V(Value)通道表示明度信息
   - I(Intensity)通道用于强度增强

2. 颜色强度解耦网络(CIDNet)：
   - 颜色分支：专门处理色调和饱和度恢复
   - 强度分支：专门处理亮度和对比度增强
   - 轻量级交叉注意力：实现分支间信息交互

3. 端到端训练：
   - 整个网络可以端到端训练
   - 支持多种损失函数组合
   - 自适应的特征学习能力

网络架构特点：
- 模块化设计：每个组件都可以独立替换和优化
- 轻量级实现：平衡性能和计算效率
- 多尺度处理：捕获不同尺度的特征信息
- 残差连接：保持信息流的稳定性

使用示例:
    >>> model = HVICIDNet(input_channels=3, output_channels=3)
    >>> low_light_img = torch.randn(1, 3, 256, 256)
    >>> enhanced_img = model(low_light_img)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional, Tuple
from loguru import logger

from ..utils.registry import register_model
from .base_model import BaseArchitecture
from .components.hvi_color_space import HVIColorSpace
from .components.lca_attention import LightweightCrossAttention
from .components.color_intensity_decoder import ColorIntensityDecoder
from .components.cidnet_blocks import ConvBlock, MultiScaleBlock, FeaturePyramidBlock


@register_model("HVI_CIDNet")
class HVICIDNet(BaseArchitecture):
    """
    HVI-CIDNet: 基于HVI颜色空间的低光图像增强网络
    
    这是完整的HVI-CIDNet实现，集成了所有核心组件：
    - HVI颜色空间转换
    - 多尺度特征提取
    - 颜色强度解耦处理
    - 轻量级交叉注意力
    - 特征金字塔融合
    
    网络工作流程：
    1. 输入RGB图像转换为HVI颜色空间
    2. 多尺度特征提取器提取层次化特征
    3. 颜色强度解耦器分别处理颜色和强度信息
    4. 交叉注意力机制实现分支间信息交互
    5. 特征融合和重建生成增强的RGB图像
    6. 可选的HVI到RGB转换验证
    
    设计优势：
    - 任务分解：将复杂的增强任务分解为专门化的子任务
    - 颜色空间优化：HVI空间更适合低光图像处理
    - 轻量级设计：在保持性能的同时控制模型复杂度
    - 模块化架构：便于理解、调试和扩展
    """
    
    def __init__(self, 
                 input_channels: int = 3,
                 output_channels: int = 3,
                 base_channels: int = 64,
                 num_scales: int = 3,
                 num_pyramid_levels: int = 3,
                 use_hvi_loss: bool = True):
        """
        初始化HVI-CIDNet网络
        
        参数:
            input_channels: 输入图像通道数（通常为3，RGB）
            output_channels: 输出图像通道数（通常为3，RGB）
            base_channels: 基础特征通道数，控制网络容量
            num_scales: 多尺度特征提取的尺度数量
            num_pyramid_levels: 特征金字塔的层数
            use_hvi_loss: 是否在HVI空间计算额外的损失
        """
        super().__init__()
        
        # 保存配置参数
        self.input_channels = input_channels
        self.output_channels = output_channels
        self.base_channels = base_channels
        self.num_scales = num_scales
        self.num_pyramid_levels = num_pyramid_levels
        self.use_hvi_loss = use_hvi_loss
        
        # 1. HVI颜色空间转换器
        self.hvi_transform = HVIColorSpace()
        
        # 2. 输入特征提取器
        # 将RGB或HVI输入转换为网络内部的特征表示
        self.input_encoder = nn.Sequential(
            ConvBlock(input_channels, base_channels, kernel_size=7, padding=3),
            ConvBlock(base_channels, base_channels, kernel_size=3),
            ConvBlock(base_channels, base_channels, kernel_size=3)
        )
        
        # 3. 多尺度特征提取器
        # 捕获不同尺度的特征信息，对于处理不同大小的低光区域很重要
        self.multiscale_extractor = MultiScaleBlock(
            in_channels=base_channels,
            out_channels=base_channels * 2,
            scales=[1, 3, 5, 7]  # 四种不同尺度的卷积核
        )
        
        # 4. 特征金字塔网络
        # 实现多尺度特征的有效融合
        self.feature_pyramid = FeaturePyramidBlock(
            channels=base_channels * 2,
            num_levels=num_pyramid_levels
        )
        
        # 5. 颜色强度解耦器
        # 这是网络的核心组件，实现颜色和强度的分离处理
        self.color_intensity_decoder = ColorIntensityDecoder(
            input_channels=base_channels * 2,
            output_channels=output_channels,
            hidden_channels=base_channels
        )
        
        # 6. 输出层
        # 最终的特征到图像的转换
        self.output_layer = nn.Sequential(
            ConvBlock(output_channels, base_channels // 2, kernel_size=3),
            ConvBlock(base_channels // 2, base_channels // 4, kernel_size=3),
            nn.Conv2d(base_channels // 4, output_channels, kernel_size=3, padding=1),
            nn.Sigmoid()  # 确保输出在[0,1]范围内
        )
        
        # 7. 残差连接权重
        # 用于控制原始输入和增强结果的融合比例
        self.residual_weight = nn.Parameter(torch.tensor(0.1))
        
        logger.info(f"✅ HVI-CIDNet初始化完成")
        logger.info(f"   - 输入通道: {input_channels}")
        logger.info(f"   - 输出通道: {output_channels}")
        logger.info(f"   - 基础通道: {base_channels}")
        logger.info(f"   - 多尺度数量: {num_scales}")
        logger.info(f"   - 金字塔层数: {num_pyramid_levels}")
        logger.info(f"   - 使用HVI损失: {use_hvi_loss}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        HVI-CIDNet前向传播
        
        完整的低光图像增强流程：
        1. 可选的HVI颜色空间转换
        2. 输入特征编码
        3. 多尺度特征提取
        4. 特征金字塔融合
        5. 颜色强度解耦处理
        6. 输出层生成最终结果
        7. 残差连接融合原始输入
        
        参数:
            x: 输入低光图像 [B, C, H, W]，值域 [0, 1]
            
        返回:
            增强后的图像 [B, C, H, W]，值域 [0, 1]
        """
        # 输入验证
        if x.dim() != 4 or x.size(1) != self.input_channels:
            raise ValueError(f"期望输入形状为 [B, {self.input_channels}, H, W]，实际得到 {x.shape}")
        
        # 保存原始输入用于残差连接
        input_residual = x
        B, C, H, W = x.size()
        
        # 第1步：输入特征编码
        # 将RGB输入转换为网络内部的特征表示
        encoded_features = self.input_encoder(x)  # [B, base_channels, H, W]
        
        # 第2步：多尺度特征提取
        # 捕获不同尺度的特征信息
        multiscale_features = self.multiscale_extractor(encoded_features)  # [B, base_channels*2, H, W]
        
        # 第3步：特征金字塔融合
        # 实现多尺度特征的有效整合
        pyramid_features = self.feature_pyramid(multiscale_features)  # [B, base_channels*2, H, W]
        
        # 第4步：颜色强度解耦处理
        # 这是网络的核心步骤，分别处理颜色和强度信息
        decoded_features = self.color_intensity_decoder(pyramid_features)  # [B, output_channels, H, W]
        
        # 第5步：输出层处理
        # 最终的特征到图像转换
        enhanced_output = self.output_layer(decoded_features)  # [B, output_channels, H, W]
        
        # 第6步：残差连接
        # 融合原始输入和增强结果，保持图像的基本结构
        final_output = enhanced_output + self.residual_weight * input_residual
        
        # 确保输出在有效范围内
        final_output = torch.clamp(final_output, 0.0, 1.0)
        
        return final_output
    
    def forward_with_hvi(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        带HVI中间结果的前向传播
        
        这个方法返回更多的中间结果，便于分析和调试：
        - 增强的RGB图像
        - HVI颜色空间表示
        - 重建的RGB图像（用于验证HVI转换质量）
        
        参数:
            x: 输入RGB图像 [B, 3, H, W]
            
        返回:
            元组 (enhanced_rgb, hvi_representation, reconstructed_rgb)
        """
        # 标准前向传播
        enhanced_rgb = self.forward(x)
        
        # HVI颜色空间转换
        hvi_representation = self.hvi_transform.rgb_to_hvi(x)
        reconstructed_rgb = self.hvi_transform.hvi_to_rgb(hvi_representation)
        
        return enhanced_rgb, hvi_representation, reconstructed_rgb
    
    def get_feature_maps(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        获取中间特征图（用于可视化和分析）
        
        参数:
            x: 输入图像
            
        返回:
            包含各阶段特征图的字典
        """
        feature_maps = {}
        
        # 输入特征编码
        encoded = self.input_encoder(x)
        feature_maps['encoded_features'] = encoded
        
        # 多尺度特征
        multiscale = self.multiscale_extractor(encoded)
        feature_maps['multiscale_features'] = multiscale
        
        # 特征金字塔
        pyramid = self.feature_pyramid(multiscale)
        feature_maps['pyramid_features'] = pyramid
        
        # 颜色强度分支特征
        color_feat, intensity_feat = self.color_intensity_decoder.get_branch_features(pyramid)
        feature_maps['color_features'] = color_feat
        feature_maps['intensity_features'] = intensity_feat
        
        return feature_maps
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取模型配置用于序列化
        
        返回:
            包含模型配置的字典
        """
        return {
            "type": "HVI_CIDNet",
            "input_channels": self.input_channels,
            "output_channels": self.output_channels,
            "base_channels": self.base_channels,
            "num_scales": self.num_scales,
            "num_pyramid_levels": self.num_pyramid_levels,
            "use_hvi_loss": self.use_hvi_loss
        }
    
    @property
    def num_parameters(self) -> int:
        """获取模型总参数数量"""
        return sum(p.numel() for p in self.parameters())
    
    @property
    def num_trainable_parameters(self) -> int:
        """获取模型可训练参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)


def test_hvi_cidnet():
    """
    HVI-CIDNet网络的测试函数
    
    验证网络的功能正确性和性能特征
    """
    print("🧪 开始HVI-CIDNet网络测试...")
    
    # 创建网络
    model = HVICIDNet(
        input_channels=3,
        output_channels=3,
        base_channels=32,  # 使用较小的通道数进行测试
        num_scales=3,
        num_pyramid_levels=2
    )
    
    # 测试1：基本前向传播
    print("📋 测试1：基本前向传播")
    input_img = torch.randn(2, 3, 128, 128)
    output_img = model(input_img)
    
    print(f"   输入形状: {input_img.shape}")
    print(f"   输出形状: {output_img.shape}")
    print(f"   输出值域: [{output_img.min():.3f}, {output_img.max():.3f}]")
    
    # 测试2：HVI模式前向传播
    print("📋 测试2：HVI模式前向传播")
    enhanced, hvi, reconstructed = model.forward_with_hvi(input_img)
    print(f"   增强图像形状: {enhanced.shape}")
    print(f"   HVI表示形状: {hvi.shape}")
    print(f"   重建图像形状: {reconstructed.shape}")
    
    # 测试3：特征图分析
    print("📋 测试3：特征图分析")
    feature_maps = model.get_feature_maps(input_img)
    for name, feat in feature_maps.items():
        print(f"   {name}: {feat.shape}")
    
    # 测试4：参数量统计
    print("📋 测试4：参数量统计")
    total_params = model.num_parameters
    trainable_params = model.num_trainable_parameters
    print(f"   总参数量: {total_params:,}")
    print(f"   可训练参数量: {trainable_params:,}")
    
    # 测试5：梯度流测试
    print("📋 测试5：梯度流测试")
    loss = output_img.mean()
    loss.backward()
    
    grad_norm = 0
    for p in model.parameters():
        if p.grad is not None:
            grad_norm += p.grad.norm().item() ** 2
    grad_norm = grad_norm ** 0.5
    print(f"   梯度范数: {grad_norm:.6f}")
    
    print("✅ HVI-CIDNet网络测试完成！")


if __name__ == "__main__":
    test_hvi_cidnet()
