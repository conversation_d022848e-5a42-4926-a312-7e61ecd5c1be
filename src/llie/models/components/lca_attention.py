"""
轻量级交叉注意力(LCA)模块

本模块实现了HVI-CIDNet论文中提出的Lightweight Cross-Attention机制。
LCA是专门为低光图像增强设计的注意力模块，具有以下特点：

核心功能：
- 颜色分支与强度分支的交叉注意力计算
- 轻量级设计，计算效率高
- 多尺度特征融合能力
- 残差连接保持信息流

设计原理：
- 通过交叉注意力机制让颜色信息和强度信息相互指导
- 使用深度可分离卷积降低计算复杂度
- 采用通道注意力和空间注意力的组合策略

使用示例:
    >>> lca = LightweightCrossAttention(channels=64)
    >>> color_feat = torch.randn(1, 64, 32, 32)
    >>> intensity_feat = torch.randn(1, 64, 32, 32)
    >>> fused_feat = lca(color_feat, intensity_feat)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
import math


class ChannelAttention(nn.Module):
    """
    通道注意力模块
    
    通过全局平均池化和全连接层计算通道重要性权重，
    让网络自动学习哪些通道对当前任务更重要。
    
    设计特点：
    - 使用全局平均池化压缩空间维度
    - 通过两层全连接网络学习通道关系
    - 使用Sigmoid激活函数生成注意力权重
    """
    
    def __init__(self, channels: int, reduction: int = 16):
        """
        初始化通道注意力模块
        
        参数:
            channels: 输入特征的通道数
            reduction: 通道压缩比例，用于控制中间层的大小
        """
        super().__init__()
        
        # 全局平均池化，将空间维度压缩为1x1
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 通道注意力网络：压缩->激活->恢复->Sigmoid
        self.attention_net = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播计算通道注意力权重
        
        参数:
            x: 输入特征图 [B, C, H, W]
            
        返回:
            加权后的特征图 [B, C, H, W]
        """
        B, C, H, W = x.size()
        
        # 全局平均池化: [B, C, H, W] -> [B, C, 1, 1]
        pooled = self.global_pool(x)
        
        # 展平为向量: [B, C, 1, 1] -> [B, C]
        pooled = pooled.view(B, C)
        
        # 计算注意力权重: [B, C] -> [B, C]
        attention_weights = self.attention_net(pooled)
        
        # 重塑为特征图形状: [B, C] -> [B, C, 1, 1]
        attention_weights = attention_weights.view(B, C, 1, 1)
        
        # 应用注意力权重
        return x * attention_weights


class SpatialAttention(nn.Module):
    """
    空间注意力模块
    
    通过分析特征图的空间分布，计算每个空间位置的重要性权重。
    这对于低光图像增强特别重要，因为不同区域的光照条件差异很大。
    
    设计特点：
    - 使用通道维度的统计信息（均值和最大值）
    - 通过卷积网络学习空间注意力模式
    - 生成与输入相同空间尺寸的注意力图
    """
    
    def __init__(self, kernel_size: int = 7):
        """
        初始化空间注意力模块
        
        参数:
            kernel_size: 卷积核大小，控制感受野范围
        """
        super().__init__()
        
        # 空间注意力卷积：输入2通道（均值+最大值），输出1通道（注意力图）
        self.spatial_conv = nn.Conv2d(
            in_channels=2, 
            out_channels=1, 
            kernel_size=kernel_size,
            padding=kernel_size // 2,
            bias=False
        )
        
        # Sigmoid激活函数生成[0,1]范围的注意力权重
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播计算空间注意力权重
        
        参数:
            x: 输入特征图 [B, C, H, W]
            
        返回:
            加权后的特征图 [B, C, H, W]
        """
        # 计算通道维度的统计信息
        # 平均值：反映整体激活水平
        avg_pool = torch.mean(x, dim=1, keepdim=True)  # [B, 1, H, W]
        
        # 最大值：反映最强激活位置
        max_pool, _ = torch.max(x, dim=1, keepdim=True)  # [B, 1, H, W]
        
        # 拼接统计信息作为空间注意力的输入
        spatial_input = torch.cat([avg_pool, max_pool], dim=1)  # [B, 2, H, W]
        
        # 通过卷积网络计算空间注意力图
        attention_map = self.spatial_conv(spatial_input)  # [B, 1, H, W]
        attention_map = self.sigmoid(attention_map)
        
        # 应用空间注意力权重
        return x * attention_map


class LightweightCrossAttention(nn.Module):
    """
    轻量级交叉注意力模块
    
    这是HVI-CIDNet的核心组件，实现颜色分支和强度分支之间的信息交互。
    通过交叉注意力机制，让两个分支能够相互指导和增强。
    
    核心思想：
    - 颜色分支关注图像的色彩信息恢复
    - 强度分支关注图像的亮度信息增强  
    - 交叉注意力让两者协同工作，获得更好的增强效果
    
    技术特点：
    - 轻量级设计：使用深度可分离卷积降低参数量
    - 多尺度融合：结合通道注意力和空间注意力
    - 残差连接：保持信息流的稳定性
    """
    
    def __init__(self, 
                 channels: int, 
                 reduction: int = 16,
                 spatial_kernel: int = 7):
        """
        初始化轻量级交叉注意力模块
        
        参数:
            channels: 特征通道数
            reduction: 通道注意力的压缩比例
            spatial_kernel: 空间注意力的卷积核大小
        """
        super().__init__()
        
        self.channels = channels
        
        # 通道注意力模块：学习通道间的重要性关系
        self.channel_attention = ChannelAttention(channels, reduction)
        
        # 空间注意力模块：学习空间位置的重要性关系
        self.spatial_attention = SpatialAttention(spatial_kernel)
        
        # 交叉注意力的特征变换网络
        # 使用1x1卷积进行特征维度变换，降低计算复杂度
        self.query_conv = nn.Conv2d(channels, channels // 8, 1, bias=False)
        self.key_conv = nn.Conv2d(channels, channels // 8, 1, bias=False)
        self.value_conv = nn.Conv2d(channels, channels, 1, bias=False)
        
        # 输出投影层
        self.output_conv = nn.Conv2d(channels, channels, 1, bias=False)
        
        # 归一化层
        self.norm1 = nn.LayerNorm(channels)
        self.norm2 = nn.LayerNorm(channels)
        
        # Dropout用于正则化
        self.dropout = nn.Dropout(0.1)
        
        # 初始化注意力权重缩放因子
        self.scale = (channels // 8) ** -0.5
    
    def cross_attention(self, 
                       query_feat: torch.Tensor, 
                       key_feat: torch.Tensor, 
                       value_feat: torch.Tensor) -> torch.Tensor:
        """
        计算交叉注意力
        
        这是注意力机制的核心计算，让query特征能够关注到key特征中的重要信息，
        并从value特征中提取相应的表示。
        
        参数:
            query_feat: 查询特征（通常是当前分支的特征）
            key_feat: 键特征（通常是另一个分支的特征）
            value_feat: 值特征（通常与键特征相同）
            
        返回:
            交叉注意力增强后的特征
        """
        B, C, H, W = query_feat.size()
        
        # 生成查询、键、值向量
        Q = self.query_conv(query_feat).view(B, -1, H * W)  # [B, C//8, H*W]
        K = self.key_conv(key_feat).view(B, -1, H * W)      # [B, C//8, H*W]
        V = self.value_conv(value_feat).view(B, -1, H * W)  # [B, C, H*W]
        
        # 计算注意力分数：Q^T * K
        attention_scores = torch.bmm(Q.transpose(1, 2), K) * self.scale  # [B, H*W, H*W]
        
        # 应用Softmax归一化
        attention_weights = F.softmax(attention_scores, dim=-1)
        
        # 应用Dropout正则化
        attention_weights = self.dropout(attention_weights)
        
        # 计算加权特征：attention_weights * V
        attended_feat = torch.bmm(V, attention_weights.transpose(1, 2))  # [B, C, H*W]
        
        # 重塑回特征图形状
        attended_feat = attended_feat.view(B, C, H, W)
        
        return attended_feat
    
    def forward(self, 
                color_feat: torch.Tensor, 
                intensity_feat: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播：计算颜色和强度特征的交叉注意力
        
        处理流程：
        1. 分别对两个分支应用通道注意力和空间注意力
        2. 计算双向交叉注意力（颜色->强度，强度->颜色）
        3. 应用残差连接和归一化
        4. 返回增强后的特征
        
        参数:
            color_feat: 颜色分支特征 [B, C, H, W]
            intensity_feat: 强度分支特征 [B, C, H, W]
            
        返回:
            元组 (enhanced_color, enhanced_intensity)
            - enhanced_color: 增强后的颜色特征
            - enhanced_intensity: 增强后的强度特征
        """
        # 保存原始特征用于残差连接
        color_residual = color_feat
        intensity_residual = intensity_feat
        
        # 第一步：应用通道注意力
        color_feat = self.channel_attention(color_feat)
        intensity_feat = self.channel_attention(intensity_feat)
        
        # 第二步：应用空间注意力
        color_feat = self.spatial_attention(color_feat)
        intensity_feat = self.spatial_attention(intensity_feat)
        
        # 第三步：计算交叉注意力
        # 颜色特征关注强度特征的信息
        color_cross = self.cross_attention(color_feat, intensity_feat, intensity_feat)
        
        # 强度特征关注颜色特征的信息
        intensity_cross = self.cross_attention(intensity_feat, color_feat, color_feat)
        
        # 第四步：特征融合和输出投影
        enhanced_color = self.output_conv(color_cross)
        enhanced_intensity = self.output_conv(intensity_cross)
        
        # 第五步：残差连接和归一化
        enhanced_color = enhanced_color + color_residual
        enhanced_intensity = enhanced_intensity + intensity_residual
        
        # 应用Layer Normalization（需要调整维度）
        B, C, H, W = enhanced_color.size()
        enhanced_color = enhanced_color.permute(0, 2, 3, 1).contiguous()  # [B, H, W, C]
        enhanced_color = self.norm1(enhanced_color)
        enhanced_color = enhanced_color.permute(0, 3, 1, 2).contiguous()  # [B, C, H, W]
        
        enhanced_intensity = enhanced_intensity.permute(0, 2, 3, 1).contiguous()
        enhanced_intensity = self.norm2(enhanced_intensity)
        enhanced_intensity = enhanced_intensity.permute(0, 3, 1, 2).contiguous()
        
        return enhanced_color, enhanced_intensity


def test_lca_attention():
    """
    LCA注意力模块的测试函数
    
    验证模块的功能正确性和性能特征，包括：
    - 基本前向传播
    - 输出形状验证
    - 参数量统计
    - 计算效率测试
    """
    print("🧪 开始LCA注意力模块测试...")
    
    # 创建LCA模块
    channels = 64
    lca = LightweightCrossAttention(channels=channels)
    
    # 测试1：基本功能测试
    print("📋 测试1：基本功能测试")
    color_feat = torch.randn(2, channels, 32, 32)
    intensity_feat = torch.randn(2, channels, 32, 32)
    
    enhanced_color, enhanced_intensity = lca(color_feat, intensity_feat)
    
    print(f"   输入颜色特征形状: {color_feat.shape}")
    print(f"   输入强度特征形状: {intensity_feat.shape}")
    print(f"   输出颜色特征形状: {enhanced_color.shape}")
    print(f"   输出强度特征形状: {enhanced_intensity.shape}")
    
    # 测试2：参数量统计
    print("📋 测试2：参数量统计")
    total_params = sum(p.numel() for p in lca.parameters())
    trainable_params = sum(p.numel() for p in lca.parameters() if p.requires_grad)
    print(f"   总参数量: {total_params:,}")
    print(f"   可训练参数量: {trainable_params:,}")
    
    # 测试3：梯度流测试
    print("📋 测试3：梯度流测试")
    loss = enhanced_color.mean() + enhanced_intensity.mean()
    loss.backward()
    
    grad_norm = 0
    for p in lca.parameters():
        if p.grad is not None:
            grad_norm += p.grad.norm().item() ** 2
    grad_norm = grad_norm ** 0.5
    print(f"   梯度范数: {grad_norm:.6f}")
    
    print("✅ LCA注意力模块测试完成！")


if __name__ == "__main__":
    test_lca_attention()
