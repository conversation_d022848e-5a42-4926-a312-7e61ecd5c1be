"""
CIDNet基础构建块模块

本模块实现了HVI-CIDNet网络的基础构建块，包括：
- 基础卷积块：标准的卷积-归一化-激活组合
- 深度可分离卷积块：轻量级的卷积实现
- 多尺度特征提取块：捕获不同尺度的特征
- 特征金字塔块：实现多尺度特征融合

设计原则：
- 模块化：每个块都是独立的功能单元
- 轻量级：优化参数量和计算复杂度
- 可复用：可以在不同的网络架构中重复使用
- 高效性：平衡性能和计算效率

这些基础块是构建CIDNet网络的基本单元，
通过组合这些块可以构建出复杂的网络架构。

使用示例:
    >>> conv_block = ConvBlock(64, 128, kernel_size=3)
    >>> x = torch.randn(1, 64, 32, 32)
    >>> output = conv_block(x)  # [1, 128, 32, 32]
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Union


class ConvBlock(nn.Module):
    """
    基础卷积块
    
    实现标准的卷积-归一化-激活组合，这是深度学习网络的基本构建单元。
    支持不同的归一化方式和激活函数，提供灵活的配置选项。
    
    结构：卷积 -> 归一化 -> 激活函数
    
    特点：
    - 支持多种归一化方式（BatchNorm, InstanceNorm, LayerNorm）
    - 支持多种激活函数（ReLU, LeakyReLU, GELU等）
    - 可配置的卷积参数（核大小、步长、填充等）
    """
    
    def __init__(self, 
                 in_channels: int, 
                 out_channels: int,
                 kernel_size: int = 3,
                 stride: int = 1,
                 padding: Optional[int] = None,
                 dilation: int = 1,
                 groups: int = 1,
                 bias: bool = False,
                 norm_type: str = 'batch',
                 activation: str = 'relu'):
        """
        初始化基础卷积块
        
        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数
            kernel_size: 卷积核大小
            stride: 步长
            padding: 填充大小，如果为None则自动计算
            dilation: 膨胀率
            groups: 分组卷积的组数
            bias: 是否使用偏置
            norm_type: 归一化类型 ('batch', 'instance', 'layer', 'none')
            activation: 激活函数类型 ('relu', 'leaky_relu', 'gelu', 'none')
        """
        super().__init__()
        
        # 自动计算填充以保持特征图尺寸
        if padding is None:
            padding = (kernel_size - 1) // 2 * dilation
        
        # 卷积层
        self.conv = nn.Conv2d(
            in_channels=in_channels,
            out_channels=out_channels,
            kernel_size=kernel_size,
            stride=stride,
            padding=padding,
            dilation=dilation,
            groups=groups,
            bias=bias
        )
        
        # 归一化层
        if norm_type == 'batch':
            self.norm = nn.BatchNorm2d(out_channels)
        elif norm_type == 'instance':
            self.norm = nn.InstanceNorm2d(out_channels)
        elif norm_type == 'layer':
            self.norm = nn.GroupNorm(1, out_channels)  # LayerNorm的2D版本
        elif norm_type == 'none':
            self.norm = nn.Identity()
        else:
            raise ValueError(f"不支持的归一化类型: {norm_type}")
        
        # 激活函数
        if activation == 'relu':
            self.activation = nn.ReLU(inplace=True)
        elif activation == 'leaky_relu':
            self.activation = nn.LeakyReLU(0.2, inplace=True)
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'none':
            self.activation = nn.Identity()
        else:
            raise ValueError(f"不支持的激活函数类型: {activation}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        x = self.conv(x)
        x = self.norm(x)
        x = self.activation(x)
        return x


class DepthwiseSeparableConv(nn.Module):
    """
    深度可分离卷积块
    
    深度可分离卷积是一种轻量级的卷积实现，将标准卷积分解为：
    1. 深度卷积（Depthwise Convolution）：每个输入通道独立进行卷积
    2. 逐点卷积（Pointwise Convolution）：1x1卷积进行通道间信息交互
    
    优势：
    - 大幅减少参数量和计算量
    - 保持相似的表达能力
    - 适合移动端和资源受限的环境
    
    参数量对比：
    - 标准卷积：K×K×C_in×C_out
    - 深度可分离卷积：K×K×C_in + C_in×C_out
    """
    
    def __init__(self, 
                 in_channels: int, 
                 out_channels: int,
                 kernel_size: int = 3,
                 stride: int = 1,
                 padding: Optional[int] = None,
                 dilation: int = 1,
                 bias: bool = False):
        """
        初始化深度可分离卷积块
        
        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数
            kernel_size: 卷积核大小
            stride: 步长
            padding: 填充大小
            dilation: 膨胀率
            bias: 是否使用偏置
        """
        super().__init__()
        
        if padding is None:
            padding = (kernel_size - 1) // 2 * dilation
        
        # 深度卷积：每个输入通道独立进行卷积
        self.depthwise = nn.Conv2d(
            in_channels=in_channels,
            out_channels=in_channels,
            kernel_size=kernel_size,
            stride=stride,
            padding=padding,
            dilation=dilation,
            groups=in_channels,  # 关键：groups=in_channels实现深度卷积
            bias=bias
        )
        
        # 深度卷积后的归一化和激活
        self.bn1 = nn.BatchNorm2d(in_channels)
        self.relu1 = nn.ReLU(inplace=True)
        
        # 逐点卷积：1x1卷积进行通道间信息交互
        self.pointwise = nn.Conv2d(
            in_channels=in_channels,
            out_channels=out_channels,
            kernel_size=1,
            stride=1,
            padding=0,
            bias=bias
        )
        
        # 逐点卷积后的归一化和激活
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.relu2 = nn.ReLU(inplace=True)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 深度卷积
        x = self.depthwise(x)
        x = self.bn1(x)
        x = self.relu1(x)
        
        # 逐点卷积
        x = self.pointwise(x)
        x = self.bn2(x)
        x = self.relu2(x)
        
        return x


class MultiScaleBlock(nn.Module):
    """
    多尺度特征提取块
    
    通过并行的不同尺度卷积核捕获多尺度特征信息。
    这对于低光图像增强特别重要，因为需要同时处理：
    - 细节信息（小尺度特征）
    - 全局结构（大尺度特征）
    - 中等尺度的纹理信息
    
    结构：
    - 1x1卷积：捕获逐点特征
    - 3x3卷积：捕获局部特征
    - 5x5卷积：捕获中等尺度特征
    - 全局平均池化：捕获全局信息
    """
    
    def __init__(self, 
                 in_channels: int, 
                 out_channels: int,
                 scales: List[int] = [1, 3, 5]):
        """
        初始化多尺度特征提取块
        
        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数
            scales: 多尺度卷积核大小列表
        """
        super().__init__()
        
        self.scales = scales
        branch_channels = out_channels // (len(scales) + 1)  # +1是为了全局池化分支
        
        # 多尺度卷积分支
        self.scale_branches = nn.ModuleList()
        for scale in scales:
            if scale == 1:
                # 1x1卷积分支
                branch = nn.Sequential(
                    nn.Conv2d(in_channels, branch_channels, 1, bias=False),
                    nn.BatchNorm2d(branch_channels),
                    nn.ReLU(inplace=True)
                )
            else:
                # 其他尺度的卷积分支
                padding = scale // 2
                branch = nn.Sequential(
                    nn.Conv2d(in_channels, branch_channels, scale, padding=padding, bias=False),
                    nn.BatchNorm2d(branch_channels),
                    nn.ReLU(inplace=True)
                )
            self.scale_branches.append(branch)
        
        # 全局池化分支
        self.global_branch = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, branch_channels, 1, bias=False),
            nn.BatchNorm2d(branch_channels),
            nn.ReLU(inplace=True)
        )
        
        # 特征融合层
        total_channels = branch_channels * (len(scales) + 1)
        if total_channels != out_channels:
            self.fusion = nn.Conv2d(total_channels, out_channels, 1, bias=False)
        else:
            self.fusion = nn.Identity()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        B, C, H, W = x.size()
        
        # 多尺度分支特征提取
        branch_features = []
        
        # 处理各个尺度的卷积分支
        for branch in self.scale_branches:
            feat = branch(x)
            branch_features.append(feat)
        
        # 处理全局池化分支
        global_feat = self.global_branch(x)
        # 上采样到原始尺寸
        global_feat = F.interpolate(global_feat, size=(H, W), mode='bilinear', align_corners=False)
        branch_features.append(global_feat)
        
        # 拼接所有分支的特征
        fused_features = torch.cat(branch_features, dim=1)
        
        # 特征融合
        output = self.fusion(fused_features)
        
        return output


class FeaturePyramidBlock(nn.Module):
    """
    特征金字塔块
    
    实现特征金字塔网络(FPN)的基本结构，用于多尺度特征融合。
    通过自顶向下的路径和横向连接，将不同尺度的特征进行有效融合。
    
    特点：
    - 自顶向下的特征传播
    - 横向连接融合同尺度特征
    - 多尺度信息的有效整合
    """
    
    def __init__(self, 
                 channels: int,
                 num_levels: int = 3):
        """
        初始化特征金字塔块
        
        参数:
            channels: 特征通道数
            num_levels: 金字塔层数
        """
        super().__init__()
        
        self.num_levels = num_levels
        
        # 下采样路径：逐步降低分辨率
        self.down_path = nn.ModuleList()
        for i in range(num_levels - 1):
            self.down_path.append(
                nn.Sequential(
                    nn.Conv2d(channels, channels, 3, stride=2, padding=1, bias=False),
                    nn.BatchNorm2d(channels),
                    nn.ReLU(inplace=True)
                )
            )
        
        # 上采样路径：逐步恢复分辨率
        self.up_path = nn.ModuleList()
        for i in range(num_levels - 1):
            self.up_path.append(
                nn.Sequential(
                    nn.ConvTranspose2d(channels, channels, 4, stride=2, padding=1, bias=False),
                    nn.BatchNorm2d(channels),
                    nn.ReLU(inplace=True)
                )
            )
        
        # 横向连接：融合同尺度特征
        self.lateral_convs = nn.ModuleList()
        for i in range(num_levels):
            self.lateral_convs.append(
                nn.Sequential(
                    nn.Conv2d(channels, channels, 1, bias=False),
                    nn.BatchNorm2d(channels),
                    nn.ReLU(inplace=True)
                )
            )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 下采样路径：构建特征金字塔
        pyramid_features = [x]
        current = x
        
        for down_conv in self.down_path:
            current = down_conv(current)
            pyramid_features.append(current)
        
        # 上采样路径：自顶向下融合特征
        for i in range(self.num_levels - 2, -1, -1):
            # 上采样高层特征
            upsampled = self.up_path[i](pyramid_features[i + 1])
            
            # 横向连接：融合同尺度特征
            lateral = self.lateral_convs[i](pyramid_features[i])
            
            # 特征融合
            pyramid_features[i] = upsampled + lateral
        
        return pyramid_features[0]


def test_cidnet_blocks():
    """
    CIDNet基础构建块的测试函数
    
    验证各个构建块的功能正确性和性能特征
    """
    print("🧪 开始CIDNet基础构建块测试...")
    
    # 测试输入
    x = torch.randn(2, 64, 32, 32)
    
    # 测试1：基础卷积块
    print("📋 测试1：基础卷积块")
    conv_block = ConvBlock(64, 128, kernel_size=3)
    out1 = conv_block(x)
    print(f"   输入形状: {x.shape}")
    print(f"   输出形状: {out1.shape}")
    
    # 测试2：深度可分离卷积块
    print("📋 测试2：深度可分离卷积块")
    dsconv_block = DepthwiseSeparableConv(64, 128)
    out2 = dsconv_block(x)
    print(f"   输出形状: {out2.shape}")
    
    # 参数量对比
    conv_params = sum(p.numel() for p in conv_block.parameters())
    dsconv_params = sum(p.numel() for p in dsconv_block.parameters())
    print(f"   标准卷积参数量: {conv_params:,}")
    print(f"   深度可分离卷积参数量: {dsconv_params:,}")
    print(f"   参数量减少: {(1 - dsconv_params/conv_params)*100:.1f}%")
    
    # 测试3：多尺度特征提取块
    print("📋 测试3：多尺度特征提取块")
    ms_block = MultiScaleBlock(64, 128, scales=[1, 3, 5])
    out3 = ms_block(x)
    print(f"   输出形状: {out3.shape}")
    
    # 测试4：特征金字塔块
    print("📋 测试4：特征金字塔块")
    fp_block = FeaturePyramidBlock(64, num_levels=3)
    out4 = fp_block(x)
    print(f"   输出形状: {out4.shape}")
    
    print("✅ CIDNet基础构建块测试完成！")


if __name__ == "__main__":
    test_cidnet_blocks()
