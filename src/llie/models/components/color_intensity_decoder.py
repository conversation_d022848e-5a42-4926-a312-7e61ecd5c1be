"""
颜色强度解耦器模块

本模块实现了HVI-CIDNet中的颜色和强度解耦处理机制。
该模块是CIDNet架构的核心组件，负责将HVI颜色空间的特征分解为：
- 颜色分支：专门处理色调和饱和度信息
- 强度分支：专门处理亮度和对比度信息

设计理念：
- 分而治之：将复杂的低光增强任务分解为颜色恢复和亮度增强两个子任务
- 专门化处理：每个分支针对特定的视觉属性进行优化
- 协同工作：通过交叉注意力机制实现分支间的信息交互

技术特点：
- 多尺度特征提取
- 残差连接保持信息流
- 自适应特征融合
- 轻量级网络设计

使用示例:
    >>> decoder = ColorIntensityDecoder(input_channels=64, output_channels=3)
    >>> hvi_features = torch.randn(1, 64, 64, 64)
    >>> enhanced_rgb = decoder(hvi_features)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, List, Optional
from .lca_attention import LightweightCrossAttention


class ResidualBlock(nn.Module):
    """
    残差块
    
    实现标准的残差连接结构，用于构建深层网络的基础单元。
    残差连接有助于解决梯度消失问题，让网络能够训练得更深。
    
    结构：输入 -> 卷积 -> 归一化 -> 激活 -> 卷积 -> 归一化 -> (+输入) -> 激活
    """
    
    def __init__(self, channels: int, kernel_size: int = 3):
        """
        初始化残差块
        
        参数:
            channels: 特征通道数
            kernel_size: 卷积核大小
        """
        super().__init__()
        
        padding = kernel_size // 2
        
        # 第一个卷积层
        self.conv1 = nn.Conv2d(channels, channels, kernel_size, padding=padding, bias=False)
        self.bn1 = nn.BatchNorm2d(channels)
        
        # 第二个卷积层
        self.conv2 = nn.Conv2d(channels, channels, kernel_size, padding=padding, bias=False)
        self.bn2 = nn.BatchNorm2d(channels)
        
        # 激活函数
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        residual = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        # 残差连接
        out += residual
        out = self.relu(out)
        
        return out


class ColorBranch(nn.Module):
    """
    颜色分支网络
    
    专门负责处理图像的颜色信息，包括色调恢复和饱和度增强。
    在低光条件下，图像的颜色信息往往会失真或饱和度降低，
    颜色分支的任务是恢复自然、鲜艳的颜色表现。
    
    设计特点：
    - 多尺度特征提取：捕获不同尺度的颜色模式
    - 色调保持：确保颜色恢复的自然性
    - 饱和度增强：提升图像的视觉吸引力
    """
    
    def __init__(self, input_channels: int, hidden_channels: int = 64):
        """
        初始化颜色分支
        
        参数:
            input_channels: 输入特征通道数
            hidden_channels: 隐藏层通道数
        """
        super().__init__()
        
        # 特征提取层
        self.feature_extract = nn.Sequential(
            nn.Conv2d(input_channels, hidden_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(hidden_channels, hidden_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True)
        )
        
        # 残差块序列：深度特征学习
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(hidden_channels) for _ in range(4)
        ])
        
        # 颜色特征增强层
        self.color_enhance = nn.Sequential(
            nn.Conv2d(hidden_channels, hidden_channels, 1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True),
            
            # 使用深度可分离卷积降低参数量
            nn.Conv2d(hidden_channels, hidden_channels, 3, padding=1, groups=hidden_channels, bias=False),
            nn.Conv2d(hidden_channels, hidden_channels, 1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        颜色分支前向传播
        
        参数:
            x: 输入特征 [B, C, H, W]
            
        返回:
            颜色特征 [B, hidden_channels, H, W]
        """
        # 特征提取
        feat = self.feature_extract(x)
        
        # 通过残差块进行深度特征学习
        for block in self.residual_blocks:
            feat = block(feat)
        
        # 颜色特征增强
        color_feat = self.color_enhance(feat)
        
        return color_feat


class IntensityBranch(nn.Module):
    """
    强度分支网络
    
    专门负责处理图像的亮度和对比度信息。
    在低光图像增强中，强度分支是最关键的组件，
    负责提升图像的整体亮度并增强局部对比度。
    
    设计特点：
    - 亮度自适应：根据输入图像的亮度分布进行自适应调整
    - 对比度增强：提升图像的局部对比度和细节清晰度
    - 噪声抑制：在增强亮度的同时抑制噪声放大
    """
    
    def __init__(self, input_channels: int, hidden_channels: int = 64):
        """
        初始化强度分支
        
        参数:
            input_channels: 输入特征通道数
            hidden_channels: 隐藏层通道数
        """
        super().__init__()
        
        # 特征提取层
        self.feature_extract = nn.Sequential(
            nn.Conv2d(input_channels, hidden_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(hidden_channels, hidden_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True)
        )
        
        # 残差块序列：深度特征学习
        self.residual_blocks = nn.ModuleList([
            ResidualBlock(hidden_channels) for _ in range(4)
        ])
        
        # 强度特征增强层
        self.intensity_enhance = nn.Sequential(
            nn.Conv2d(hidden_channels, hidden_channels, 1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True),
            
            # 使用扩张卷积增大感受野
            nn.Conv2d(hidden_channels, hidden_channels, 3, padding=2, dilation=2, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(hidden_channels, hidden_channels, 1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        强度分支前向传播
        
        参数:
            x: 输入特征 [B, C, H, W]
            
        返回:
            强度特征 [B, hidden_channels, H, W]
        """
        # 特征提取
        feat = self.feature_extract(x)
        
        # 通过残差块进行深度特征学习
        for block in self.residual_blocks:
            feat = block(feat)
        
        # 强度特征增强
        intensity_feat = self.intensity_enhance(feat)
        
        return intensity_feat


class FeatureFusion(nn.Module):
    """
    特征融合模块
    
    将颜色分支和强度分支的特征进行融合，生成最终的增强特征。
    融合过程需要平衡两个分支的贡献，确保既保持颜色的自然性，
    又实现亮度的有效增强。
    
    融合策略：
    - 自适应权重：根据输入特征自动学习融合权重
    - 多尺度融合：在不同尺度上进行特征融合
    - 残差连接：保持原始信息的传递
    """
    
    def __init__(self, channels: int):
        """
        初始化特征融合模块
        
        参数:
            channels: 特征通道数
        """
        super().__init__()
        
        # 自适应权重学习网络
        self.weight_net = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels * 2, channels // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 4, 2, 1),
            nn.Softmax(dim=1)
        )
        
        # 特征融合卷积
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(channels * 2, channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(channels, channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, color_feat: torch.Tensor, intensity_feat: torch.Tensor) -> torch.Tensor:
        """
        特征融合前向传播
        
        参数:
            color_feat: 颜色特征 [B, C, H, W]
            intensity_feat: 强度特征 [B, C, H, W]
            
        返回:
            融合特征 [B, C, H, W]
        """
        # 拼接两个分支的特征
        concat_feat = torch.cat([color_feat, intensity_feat], dim=1)  # [B, 2C, H, W]
        
        # 学习自适应融合权重
        weights = self.weight_net(concat_feat)  # [B, 2, 1, 1]
        color_weight = weights[:, 0:1]  # [B, 1, 1, 1]
        intensity_weight = weights[:, 1:2]  # [B, 1, 1, 1]
        
        # 加权融合
        weighted_color = color_feat * color_weight
        weighted_intensity = intensity_feat * intensity_weight
        
        # 特征融合
        fused_feat = self.fusion_conv(torch.cat([weighted_color, weighted_intensity], dim=1))
        
        return fused_feat


class ColorIntensityDecoder(nn.Module):
    """
    颜色强度解耦器
    
    这是HVI-CIDNet的核心解码器，实现了颜色和强度的分离处理机制。
    通过将复杂的低光增强任务分解为颜色恢复和亮度增强两个子任务，
    每个子任务都可以进行专门化的优化。
    
    工作流程：
    1. 输入HVI特征分别送入颜色分支和强度分支
    2. 两个分支并行处理，提取专门化特征
    3. 通过LCA注意力机制实现分支间信息交互
    4. 特征融合模块生成最终的增强特征
    5. 输出层将特征转换为RGB图像
    
    设计优势：
    - 任务分解：降低单一网络的学习难度
    - 专门化处理：每个分支针对特定属性优化
    - 信息交互：通过注意力机制实现协同工作
    - 端到端训练：整个网络可以联合优化
    """
    
    def __init__(self, 
                 input_channels: int, 
                 output_channels: int = 3,
                 hidden_channels: int = 64):
        """
        初始化颜色强度解耦器
        
        参数:
            input_channels: 输入特征通道数
            output_channels: 输出图像通道数（通常为3，RGB）
            hidden_channels: 隐藏层通道数
        """
        super().__init__()
        
        self.input_channels = input_channels
        self.output_channels = output_channels
        self.hidden_channels = hidden_channels
        
        # 输入特征预处理
        self.input_proj = nn.Sequential(
            nn.Conv2d(input_channels, hidden_channels, 3, padding=1, bias=False),
            nn.BatchNorm2d(hidden_channels),
            nn.ReLU(inplace=True)
        )
        
        # 颜色分支：专门处理色调和饱和度
        self.color_branch = ColorBranch(hidden_channels, hidden_channels)
        
        # 强度分支：专门处理亮度和对比度
        self.intensity_branch = IntensityBranch(hidden_channels, hidden_channels)
        
        # 轻量级交叉注意力：实现分支间信息交互
        self.cross_attention = LightweightCrossAttention(hidden_channels)
        
        # 特征融合：合并两个分支的信息
        self.feature_fusion = FeatureFusion(hidden_channels)
        
        # 输出层：将特征转换为RGB图像
        self.output_layer = nn.Sequential(
            nn.Conv2d(hidden_channels, hidden_channels // 2, 3, padding=1, bias=False),
            nn.BatchNorm2d(hidden_channels // 2),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(hidden_channels // 2, output_channels, 3, padding=1),
            nn.Sigmoid()  # 确保输出在[0,1]范围内
        )
    
    def forward(self, hvi_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播：颜色强度解耦处理
        
        参数:
            hvi_features: HVI特征 [B, input_channels, H, W]
            
        返回:
            增强的RGB图像 [B, output_channels, H, W]
        """
        # 输入特征预处理
        features = self.input_proj(hvi_features)  # [B, hidden_channels, H, W]
        
        # 分支处理：并行提取颜色和强度特征
        color_feat = self.color_branch(features)     # [B, hidden_channels, H, W]
        intensity_feat = self.intensity_branch(features)  # [B, hidden_channels, H, W]
        
        # 交叉注意力：让两个分支相互指导
        enhanced_color, enhanced_intensity = self.cross_attention(color_feat, intensity_feat)
        
        # 特征融合：合并增强后的特征
        fused_features = self.feature_fusion(enhanced_color, enhanced_intensity)
        
        # 生成最终的RGB输出
        output_rgb = self.output_layer(fused_features)
        
        return output_rgb
    
    def get_branch_features(self, hvi_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        获取分支特征（用于可视化和分析）
        
        参数:
            hvi_features: HVI特征
            
        返回:
            元组 (color_features, intensity_features)
        """
        features = self.input_proj(hvi_features)
        color_feat = self.color_branch(features)
        intensity_feat = self.intensity_branch(features)
        return color_feat, intensity_feat


def test_color_intensity_decoder():
    """
    颜色强度解耦器的测试函数
    
    验证解耦器的功能正确性和性能特征
    """
    print("🧪 开始颜色强度解耦器测试...")
    
    # 创建解耦器
    input_channels = 64
    decoder = ColorIntensityDecoder(input_channels=input_channels)
    
    # 测试1：基本功能测试
    print("📋 测试1：基本功能测试")
    hvi_features = torch.randn(2, input_channels, 64, 64)
    output_rgb = decoder(hvi_features)
    
    print(f"   输入HVI特征形状: {hvi_features.shape}")
    print(f"   输出RGB图像形状: {output_rgb.shape}")
    print(f"   输出值域: [{output_rgb.min():.3f}, {output_rgb.max():.3f}]")
    
    # 测试2：分支特征分析
    print("📋 测试2：分支特征分析")
    color_feat, intensity_feat = decoder.get_branch_features(hvi_features)
    print(f"   颜色分支特征形状: {color_feat.shape}")
    print(f"   强度分支特征形状: {intensity_feat.shape}")
    
    # 测试3：参数量统计
    print("📋 测试3：参数量统计")
    total_params = sum(p.numel() for p in decoder.parameters())
    print(f"   总参数量: {total_params:,}")
    
    print("✅ 颜色强度解耦器测试完成！")


if __name__ == "__main__":
    test_color_intensity_decoder()
