"""
HVI颜色空间转换模块

本模块实现了RGB颜色空间与HVI颜色空间之间的相互转换。
HVI颜色空间是专门为低光图像增强设计的颜色表示方法，包含：
- H (Hue): 色调通道，保持颜色的基本特征
- V (Value): 明度通道，表示像素的亮度信息  
- I (Intensity): 强度通道，用于低光增强的关键通道

设计特点：
- 支持批处理和GPU加速
- 数值稳定的数学实现
- 详细的中文注释便于学习
- 模块化设计便于替换和扩展

使用示例:
    >>> transform = HVIColorSpace()
    >>> rgb_tensor = torch.randn(1, 3, 256, 256)  # [B, C, H, W]
    >>> hvi_tensor = transform.rgb_to_hvi(rgb_tensor)
    >>> reconstructed_rgb = transform.hvi_to_rgb(hvi_tensor)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple
import math


class HVIColorSpace(nn.Module):
    """
    HVI颜色空间转换器
    
    实现RGB与HVI颜色空间的双向转换，专门为低光图像增强优化。
    HVI颜色空间将图像分解为色调、明度和强度三个独立的通道，
    便于对不同视觉属性进行针对性处理。
    
    数学原理：
    - H通道：保持颜色的色调信息，对光照变化不敏感
    - V通道：表示像素的明度，是传统亮度的改进版本
    - I通道：强度信息，专门用于低光增强处理
    
    属性:
        eps: 数值稳定性常数，防止除零错误
    """
    
    def __init__(self, eps: float = 1e-8):
        """
        初始化HVI颜色空间转换器
        
        参数:
            eps: 数值稳定性常数，用于防止除零和数值溢出
        """
        super().__init__()
        self.eps = eps
        
        # 注册RGB到YUV转换的标准系数矩阵
        # 这些系数基于ITU-R BT.601标准，用于初步的颜色空间转换
        self.register_buffer('rgb_to_yuv_matrix', torch.tensor([
            [0.299, 0.587, 0.114],      # Y (亮度) 通道系数
            [-0.14713, -0.28886, 0.436], # U (色度) 通道系数  
            [0.615, -0.51499, -0.10001]  # V (色度) 通道系数
        ], dtype=torch.float32))
        
    def rgb_to_hvi(self, rgb: torch.Tensor) -> torch.Tensor:
        """
        将RGB图像转换为HVI颜色空间
        
        转换过程：
        1. 输入验证和预处理
        2. 计算色调(H)通道 - 基于RGB的角度信息
        3. 计算明度(V)通道 - 改进的亮度表示
        4. 计算强度(I)通道 - 专门用于低光增强
        
        参数:
            rgb: RGB图像张量，形状为 [B, 3, H, W]，值域 [0, 1]
            
        返回:
            HVI图像张量，形状为 [B, 3, H, W]，各通道值域：
            - H: [0, 2π] (色调角度)
            - V: [0, 1] (明度)
            - I: [0, 1] (强度)
            
        数学公式:
            H = atan2(√3 * (G - B), 2 * R - G - B)
            V = (R + G + B) / 3
            I = max(R, G, B)
        """
        # 输入验证
        if rgb.dim() != 4 or rgb.size(1) != 3:
            raise ValueError(f"期望输入形状为 [B, 3, H, W]，实际得到 {rgb.shape}")
        
        # 分离RGB通道，添加数值稳定性
        R, G, B = rgb[:, 0:1], rgb[:, 1:2], rgb[:, 2:3]  # [B, 1, H, W]
        
        # 计算色调(H)通道
        # 使用改进的色调计算公式，对低光条件更加稳定
        numerator = math.sqrt(3) * (G - B)  # √3 * (G - B)
        denominator = 2 * R - G - B + self.eps  # 2R - G - B，添加eps防止除零
        
        # 使用atan2函数计算角度，自动处理象限
        H = torch.atan2(numerator, denominator)
        
        # 将角度范围从[-π, π]映射到[0, 2π]
        H = torch.where(H < 0, H + 2 * math.pi, H)
        
        # 归一化到[0, 1]范围便于网络处理
        H = H / (2 * math.pi)
        
        # 计算明度(V)通道
        # 使用RGB三通道的平均值作为明度，比单纯的亮度更稳定
        V = (R + G + B) / 3.0
        
        # 计算强度(I)通道  
        # 使用RGB的最大值作为强度，保留最亮的颜色信息
        I = torch.max(torch.max(R, G), B)
        
        # 组合HVI通道
        hvi = torch.cat([H, V, I], dim=1)  # [B, 3, H, W]
        
        return hvi
    
    def hvi_to_rgb(self, hvi: torch.Tensor) -> torch.Tensor:
        """
        将HVI颜色空间转换回RGB
        
        这是rgb_to_hvi的逆变换，用于将处理后的HVI图像转换回RGB格式。
        逆变换过程相对复杂，需要根据HVI的数学关系重建RGB值。
        
        参数:
            hvi: HVI图像张量，形状为 [B, 3, H, W]
            
        返回:
            RGB图像张量，形状为 [B, 3, H, W]，值域 [0, 1]
            
        注意:
            由于HVI到RGB的转换存在信息损失，重建的RGB可能与原始RGB略有差异。
            这是颜色空间转换的固有特性，在实际应用中是可接受的。
        """
        # 输入验证
        if hvi.dim() != 4 or hvi.size(1) != 3:
            raise ValueError(f"期望输入形状为 [B, 3, H, W]，实际得到 {hvi.shape}")
        
        # 分离HVI通道
        H, V, I = hvi[:, 0:1], hvi[:, 1:2], hvi[:, 2:3]  # [B, 1, H, W]
        
        # 将归一化的色调转换回弧度
        H_rad = H * 2 * math.pi
        
        # 计算色调的三角函数值
        cos_H = torch.cos(H_rad)
        sin_H = torch.sin(H_rad)
        
        # 根据HVI的数学关系重建RGB
        # 这是一个复杂的逆变换过程，基于色调角度和明度/强度信息
        
        # 计算中间变量
        sqrt3 = math.sqrt(3)
        
        # 重建RGB的数学公式（简化版本）
        # 实际实现中可能需要更复杂的逆变换算法
        R = V + (2.0 / 3.0) * (I - V) * cos_H
        G = V + (2.0 / 3.0) * (I - V) * (-0.5 * cos_H + sqrt3 / 2.0 * sin_H)
        B = V + (2.0 / 3.0) * (I - V) * (-0.5 * cos_H - sqrt3 / 2.0 * sin_H)
        
        # 确保RGB值在有效范围内
        R = torch.clamp(R, 0.0, 1.0)
        G = torch.clamp(G, 0.0, 1.0)
        B = torch.clamp(B, 0.0, 1.0)
        
        # 组合RGB通道
        rgb = torch.cat([R, G, B], dim=1)  # [B, 3, H, W]
        
        return rgb
    
    def forward(self, rgb: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播：同时返回HVI转换结果和重建的RGB
        
        这个方法便于在训练过程中同时获得转换结果和重建质量评估。
        
        参数:
            rgb: 输入RGB图像
            
        返回:
            元组 (hvi, reconstructed_rgb)
            - hvi: HVI颜色空间表示
            - reconstructed_rgb: 重建的RGB图像
        """
        hvi = self.rgb_to_hvi(rgb)
        reconstructed_rgb = self.hvi_to_rgb(hvi)
        return hvi, reconstructed_rgb


def test_hvi_color_space():
    """
    HVI颜色空间转换的测试函数
    
    验证转换的正确性和数值稳定性，包括：
    - 基本转换功能
    - 批处理支持
    - 边界条件处理
    - 重建质量评估
    """
    print("🧪 开始HVI颜色空间转换测试...")
    
    # 创建转换器
    transform = HVIColorSpace()
    
    # 测试1：基本转换功能
    print("📋 测试1：基本转换功能")
    rgb = torch.rand(2, 3, 64, 64)  # 随机RGB图像
    hvi = transform.rgb_to_hvi(rgb)
    reconstructed = transform.hvi_to_rgb(hvi)
    
    print(f"   输入RGB形状: {rgb.shape}")
    print(f"   HVI形状: {hvi.shape}")
    print(f"   重建RGB形状: {reconstructed.shape}")
    
    # 测试2：重建质量评估
    print("📋 测试2：重建质量评估")
    mse_loss = F.mse_loss(rgb, reconstructed)
    print(f"   重建MSE损失: {mse_loss.item():.6f}")
    
    # 测试3：边界条件
    print("📋 测试3：边界条件测试")
    # 纯黑图像
    black = torch.zeros(1, 3, 32, 32)
    hvi_black = transform.rgb_to_hvi(black)
    print(f"   纯黑图像HVI范围: H[{hvi_black[0,0].min():.3f}, {hvi_black[0,0].max():.3f}]")
    
    # 纯白图像
    white = torch.ones(1, 3, 32, 32)
    hvi_white = transform.rgb_to_hvi(white)
    print(f"   纯白图像HVI范围: V[{hvi_white[0,1].min():.3f}, {hvi_white[0,1].max():.3f}]")
    
    print("✅ HVI颜色空间转换测试完成！")


if __name__ == "__main__":
    test_hvi_color_space()
