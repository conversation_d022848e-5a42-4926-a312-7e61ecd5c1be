#!/usr/bin/env python3
"""
HVI与RGB色彩空间对比演示
展示HVI色彩空间相比RGB的优势
"""

import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import torchvision.transforms as transforms
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from net.HVI_transform import RGB_HVI

def compare_color_spaces(image_path):
    """对比HVI和RGB色彩空间的表示"""
    
    if not os.path.exists(image_path):
        print(f"错误: 文件 {image_path} 不存在")
        return
    
    # 加载图像
    img = Image.open(image_path).convert('RGB')
    img_tensor = transforms.ToTensor()(img).unsqueeze(0)
    
    # HVI转换
    transform = RGB_HVI()
    hvi_tensor = transform.HVIT(img_tensor)
    reconstructed_rgb = transform.PHVIT(hvi_tensor)
    
    # 转换为PIL图像
    reconstructed_img = transforms.ToPILImage()(reconstructed_rgb[0].squeeze(0))
    
    # 分离通道用于对比
    rgb_channels = img_tensor[0].detach().cpu().numpy()
    hvi_channels = hvi_tensor[0].detach().cpu().numpy()
    
    # 创建对比图
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('RGB vs HVI 色彩空间对比', fontsize=16)
    
    # RGB通道
    axes[0, 0].imshow(rgb_channels[0], cmap='Reds', vmin=0, vmax=1)
    axes[0, 0].set_title('RGB - R通道')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(rgb_channels[1], cmap='Greens', vmin=0, vmax=1)
    axes[0, 1].set_title('RGB - G通道')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(rgb_channels[2], cmap='Blues', vmin=0, vmax=1)
    axes[0, 2].set_title('RGB - B通道')
    axes[0, 2].axis('off')
    
    axes[0, 3].imshow(img)
    axes[0, 3].set_title('RGB - 合成')
    axes[0, 3].axis('off')
    
    # HVI通道
    axes[1, 0].imshow(hvi_channels[0], cmap='RdBu', vmin=-1, vmax=1)
    axes[1, 0].set_title('HVI - H通道 (色调余弦)')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(hvi_channels[1], cmap='RdBu', vmin=-1, vmax=1)
    axes[1, 1].set_title('HVI - V通道 (色调正弦)')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(hvi_channels[2], cmap='gray', vmin=0, vmax=1)
    axes[1, 2].set_title('HVI - I通道 (强度)')
    axes[1, 2].axis('off')
    
    # HVI伪彩色表示
    hvi_normalized = hvi_channels.copy()
    hvi_normalized[0] = (hvi_normalized[0] + 1) / 2  # 归一化到[0,1]
    hvi_normalized[1] = (hvi_normalized[1] + 1) / 2  # 归一化到[0,1]
    axes[1, 3].imshow(np.transpose(hvi_normalized, (1, 2, 0)))
    axes[1, 3].set_title('HVI - 伪彩色表示')
    axes[1, 3].axis('off')
    
    # 对比分析
    # 计算HSV等效表示用于对比
    hsv_img = img.convert('HSV')
    hsv_array = np.array(hsv_img)
    
    axes[2, 0].imshow(hsv_array[:, :, 0] / 255.0, cmap='hsv', vmin=0, vmax=1)
    axes[2, 0].set_title('HSV - H通道')
    axes[2, 0].axis('off')
    
    axes[2, 1].imshow(hsv_array[:, :, 1] / 255.0, cmap='viridis', vmin=0, vmax=1)
    axes[2, 1].set_title('HSV - S通道')
    axes[2, 1].axis('off')
    
    axes[2, 2].imshow(hsv_array[:, :, 2] / 255.0, cmap='gray', vmin=0, vmax=1)
    axes[2, 2].set_title('HSV - V通道')
    axes[2, 2].axis('off')
    
    axes[2, 3].imshow(reconstructed_img)
    axes[2, 3].set_title('HVI重建的RGB')
    axes[2, 3].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # 计算重建误差
    original_np = rgb_channels
    reconstructed_np = reconstructed_rgb[0].detach().cpu().numpy()
    
    mse = np.mean((original_np - reconstructed_np) ** 2)
    psnr = 20 * np.log10(1.0 / np.sqrt(mse)) if mse > 0 else float('inf')
    
    print(f"\n=== HVI重建质量评估 ===")
    print(f"MSE: {mse:.6f}")
    print(f"PSNR: {psnr:.2f} dB")
    
    return original_np, reconstructed_np

def demonstrate_low_light_advantage():
    """演示HVI在低光环境下的优势"""
    
    # 创建测试图像：渐变亮度
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    fig.suptitle('HVI在低光环境下的优势', fontsize=16)
    
    # 创建不同亮度的测试图像
    brightness_levels = [0.1, 0.3, 0.6, 1.0]
    colors = [[1.0, 0.5, 0.2], [0.2, 1.0, 0.5], [0.5, 0.2, 1.0], [1.0, 1.0, 0.2]]
    
    transform = RGB_HVI()
    
    for i, brightness in enumerate(brightness_levels):
        for j, color in enumerate(colors):
            # 创建低光图像
            low_light_color = [c * brightness for c in color]
            img_tensor = torch.tensor(low_light_color).float().view(1, 3, 1, 1)
            
            # RGB表示
            rgb_img = np.array(low_light_color).reshape(1, 1, 3)
            
            # HVI表示
            hvi_tensor = transform.HVIT(img_tensor)
            hvi_channels = hvi_tensor[0].detach().cpu().numpy()
            
            # 重建的RGB
            reconstructed_rgb = transform.PHVIT(hvi_tensor)
            reconstructed_img = reconstructed_rgb[0].detach().cpu().numpy().reshape(1, 1, 3)
            
            if i == 0:  # 第一行显示原始RGB
                axes[i, j].imshow(rgb_img)
                axes[i, j].set_title(f'RGB\n亮度={brightness:.1f}')
                axes[i, j].axis('off')
            else:  # 第二行显示HVI重建
                axes[i, j].imshow(reconstructed_img)
                axes[i, j].set_title(f'HVI重建\n亮度={brightness:.1f}')
                axes[i, j].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # 分析色彩保持能力
    print("\n=== 低光环境下色彩保持能力分析 ===")
    print("测试不同亮度下的色彩表现...")
    
    for brightness in [0.05, 0.1, 0.2, 0.5]:
        test_color = [1.0, 0.5, 0.2]  # 橙色
        low_light_color = [c * brightness for c in test_color]
        img_tensor = torch.tensor(low_light_color).float().view(1, 3, 1, 1)
        
        hvi_tensor = transform.HVIT(img_tensor)
        reconstructed_rgb = transform.PHVIT(hvi_tensor)
        
        original_norm = np.linalg.norm(test_color)
        reconstructed_norm = np.linalg.norm(reconstructed_rgb[0].detach().cpu().numpy().flatten())
        
        color_preservation = reconstructed_norm / original_norm if original_norm > 0 else 0
        
        print(f"亮度 {brightness:.2f}: 色彩保持率 {color_preservation:.2%}")

def analyze_color_separation():
    """分析HVI的色彩分离能力"""
    
    # 创建包含多种颜色的测试图像
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('HVI色彩分离能力分析', fontsize=16)
    
    # 创建色轮测试图像
    size = 100
    center = size // 2
    y, x = np.ogrid[:size, :size]
    
    # 创建色轮
    hue = np.arctan2(y - center, x - center) / (2 * np.pi) + 0.5
    saturation = np.minimum(1.0, np.sqrt((x - center)**2 + (y - center)**2) / (size // 2))
    value = np.ones((size, size))
    
    # HSV转RGB
    hsv_img = np.stack([hue * 255, saturation * 255, value * 255], axis=2).astype(np.uint8)
    rgb_img = Image.fromarray(hsv_img, 'HSV').convert('RGB')
    rgb_array = np.array(rgb_img) / 255.0
    
    # 转换到HVI空间
    rgb_tensor = torch.tensor(rgb_array).float().permute(2, 0, 1).unsqueeze(0)
    transform = RGB_HVI()
    hvi_tensor = transform.HVIT(rgb_tensor)
    hvi_array = hvi_tensor[0].detach().cpu().numpy()
    
    # 显示结果
    axes[0, 0].imshow(rgb_array)
    axes[0, 0].set_title('RGB色轮')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(hvi_array[0], cmap='RdBu', vmin=-1, vmax=1)
    axes[0, 1].set_title('H通道')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(hvi_array[1], cmap='RdBu', vmin=-1, vmax=1)
    axes[0, 2].set_title('V通道')
    axes[0, 2].axis('off')
    
    axes[1, 0].imshow(hvi_array[2], cmap='gray', vmin=0, vmax=1)
    axes[1, 0].set_title('I通道')
    axes[1, 0].axis('off')
    
    # 计算色调一致性
    reconstructed_hue = np.arctan2(hvi_array[1], hvi_array[0]) / (2 * np.pi) + 0.5
    hue_consistency = np.corrcoef(hue.flatten(), reconstructed_hue.flatten())[0, 1]
    
    axes[1, 1].imshow(reconstructed_hue, cmap='hsv', vmin=0, vmax=1)
    axes[1, 1].set_title(f'重建色调 (相关系数: {hue_consistency:.3f})')
    axes[1, 1].axis('off')
    
    # 隐藏最后一个子图
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    print(f"\n=== 色彩分离能力分析 ===")
    print(f"色调一致性相关系数: {hue_consistency:.4f}")
    print(f"H通道范围: [{hvi_array[0].min():.4f}, {hvi_array[0].max():.4f}]")
    print(f"V通道范围: [{hvi_array[1].min():.4f}, {hvi_array[1].max():.4f}]")
    print(f"I通道范围: [{hvi_array[2].min():.4f}, {hvi_array[2].max():.4f}]")

def main():
    """主函数"""
    print("=== HVI与RGB色彩空间对比演示 ===")
    print("1. 对比真实图像的RGB和HVI表示")
    print("2. 演示HVI在低光环境下的优势")
    print("3. 分析HVI的色彩分离能力")
    print("4. 运行所有演示")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == '1':
        image_path = input("请输入图像路径: ").strip()
        compare_color_spaces(image_path)
    elif choice == '2':
        demonstrate_low_light_advantage()
    elif choice == '3':
        analyze_color_separation()
    elif choice == '4':
        image_path = input("请输入图像路径: ").strip()
        if os.path.exists(image_path):
            compare_color_spaces(image_path)
        demonstrate_low_light_advantage()
        analyze_color_separation()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()