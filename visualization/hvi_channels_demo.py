#!/usr/bin/env python3
"""
HVI色彩空间通道分离演示
"""

import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import torchvision.transforms as transforms
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from net.HVI_transform import RGB_HVI

def demonstrate_hvi_channels():
    """演示HVI三个通道的含义和可视化"""
    
    # 创建一个简单的测试图像
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    fig.suptitle('HVI色彩空间通道详解', fontsize=16)
    
    # 1. 创建测试图像
    # 纯色测试
    test_colors = [
        ([1.0, 0.0, 0.0], '红色'),
        ([0.0, 1.0, 0.0], '绿色'),
        ([0.0, 0.0, 1.0], '蓝色'),
        ([1.0, 1.0, 0.0], '黄色'),
        ([1.0, 0.0, 1.0], '洋红'),
        ([0.0, 1.0, 1.0], '青色'),
        ([0.5, 0.5, 0.5], '灰色'),
        ([1.0, 1.0, 1.0], '白色')
    ]
    
    transform = RGB_HVI()
    
    print("=== HVI通道含义说明 ===")
    print("H通道: 色调的余弦分量，范围[-1, 1]")
    print("V通道: 色调的正弦分量，范围[-1, 1]")
    print("I通道: 强度/亮度，范围[0, 1]")
    print()
    
    for i, (color, name) in enumerate(test_colors):
        # 创建单色图像
        img_tensor = torch.tensor(color).float().view(1, 3, 1, 1)
        
        # 转换到HVI空间
        hvi_tensor = transform.HVIT(img_tensor)
        
        H = hvi_tensor[0, 0, 0, 0].item()
        V = hvi_tensor[0, 1, 0, 0].item()
        I = hvi_tensor[0, 2, 0, 0].item()
        
        print(f"{name}: RGB={color} -> H={H:.3f}, V={V:.3f}, I={I:.3f}")
        
        if i < 4:
            row = 0
            col = i
        else:
            row = 1
            col = i - 4
        
        # 显示颜色块
        color_patch = np.array(color).reshape(1, 1, 3)
        axes[row, col].imshow(color_patch)
        axes[row, col].set_title(f'{name}\nH={H:.2f}, V={V:.2f}, I={I:.2f}')
        axes[row, col].axis('off')
    
    plt.tight_layout()
    plt.show()

def analyze_real_image_channels(image_path):
    """分析真实图像的HVI通道"""
    if not os.path.exists(image_path):
        print(f"错误: 文件 {image_path} 不存在")
        return
    
    # 加载图像
    img = Image.open(image_path).convert('RGB')
    img_tensor = transforms.ToTensor()(img).unsqueeze(0)
    
    # 转换到HVI空间
    transform = RGB_HVI()
    hvi_tensor = transform.HVIT(img_tensor)
    
    # 分离通道
    H = hvi_tensor[0, 0, :, :].detach().cpu().numpy()
    V = hvi_tensor[0, 1, :, :].detach().cpu().numpy()
    I = hvi_tensor[0, 2, :, :].detach().cpu().numpy()
    
    # 创建详细的可视化
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    fig.suptitle(f'HVI通道分析 - {os.path.basename(image_path)}', fontsize=16)
    
    # 原始图像
    axes[0, 0].imshow(img)
    axes[0, 0].set_title('原始RGB图像')
    axes[0, 0].axis('off')
    
    # H通道
    im1 = axes[0, 1].imshow(H, cmap='RdBu', vmin=-1, vmax=1)
    axes[0, 1].set_title('H通道 (色调余弦)')
    axes[0, 1].axis('off')
    plt.colorbar(im1, ax=axes[0, 1], fraction=0.046)
    
    # V通道
    im2 = axes[0, 2].imshow(V, cmap='RdBu', vmin=-1, vmax=1)
    axes[0, 2].set_title('V通道 (色调正弦)')
    axes[0, 2].axis('off')
    plt.colorbar(im2, ax=axes[0, 2], fraction=0.046)
    
    # I通道
    im3 = axes[0, 3].imshow(I, cmap='gray', vmin=0, vmax=1)
    axes[0, 3].set_title('I通道 (强度)')
    axes[0, 3].axis('off')
    plt.colorbar(im3, ax=axes[0, 3], fraction=0.046)
    
    # 色调角度 (从H和V计算)
    hue_angle = np.arctan2(V, H) / (2 * np.pi)
    hue_angle = hue_angle % 1
    im4 = axes[1, 0].imshow(hue_angle, cmap='hsv', vmin=0, vmax=1)
    axes[1, 0].set_title('色调角度 (0-360°)')
    axes[1, 0].axis('off')
    plt.colorbar(im4, ax=axes[1, 0], fraction=0.046)
    
    # 饱和度 (从H和V计算)
    saturation = np.sqrt(H**2 + V**2)
    im5 = axes[1, 1].imshow(saturation, cmap='viridis', vmin=0, vmax=1)
    axes[1, 1].set_title('饱和度')
    axes[1, 1].axis('off')
    plt.colorbar(im5, ax=axes[1, 1], fraction=0.046)
    
    # 色彩敏感度
    k = transform.density_k
    color_sensitive = ((I * 0.5 * np.pi).sin() + 1e-8) ** k
    im6 = axes[1, 2].imshow(color_sensitive, cmap='plasma', vmin=0, vmax=1)
    axes[1, 2].set_title(f'色彩敏感度 (k={k})')
    axes[1, 2].axis('off')
    plt.colorbar(im6, ax=axes[1, 2], fraction=0.046)
    
    # 隐藏最后一个子图
    axes[1, 3].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # 打印统计信息
    print(f"\n=== {os.path.basename(image_path)} HVI分析 ===")
    print(f"H通道: 均值={H.mean():.4f}, 标准差={H.std():.4f}, 范围=[{H.min():.4f}, {H.max():.4f}]")
    print(f"V通道: 均值={V.mean():.4f}, 标准差={V.std():.4f}, 范围=[{V.min():.4f}, {V.max():.4f}]")
    print(f"I通道: 均值={I.mean():.4f}, 标准差={I.std():.4f}, 范围=[{I.min():.4f}, {I.max():.4f}]")
    print(f"色调角度: 均值={hue_angle.mean():.4f}, 标准差={hue_angle.std():.4f}")
    print(f"饱和度: 均值={saturation.mean():.4f}, 标准差={saturation.std():.4f}")
    print(f"色彩敏感度: 均值={color_sensitive.mean():.4f}, 标准差={color_sensitive.std():.4f}")

def main():
    """主函数"""
    print("=== HVI色彩空间通道分离演示 ===")
    print("1. 理解HVI通道含义 (使用纯色测试)")
    print("2. 分析真实图像的HVI通道")
    
    choice = input("请选择 (1-2): ").strip()
    
    if choice == '1':
        demonstrate_hvi_channels()
    elif choice == '2':
        image_path = input("请输入图像路径: ").strip()
        analyze_real_image_channels(image_path)
    else:
        print("无效选择")

if __name__ == "__main__":
    main()