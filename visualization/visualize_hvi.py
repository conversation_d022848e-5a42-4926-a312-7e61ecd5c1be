#!/usr/bin/env python3
"""
HVI色彩空间可视化工具
用于查看和理解图像在HVI色彩空间中的表示
"""

import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import torchvision.transforms as transforms
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from net.HVI_transform import RGB_HVI

class HVIVisualizer:
    def __init__(self):
        self.transform = RGB_HVI()
        
    def load_image(self, image_path):
        """加载图像并转换为tensor"""
        img = Image.open(image_path).convert('RGB')
        img_tensor = transforms.ToTensor()(img).unsqueeze(0)
        return img_tensor, img
    
    def visualize_hvi_components(self, image_path, save_path=None):
        """可视化HVI三个通道的分离效果"""
        # 加载图像
        img_tensor, original_img = self.load_image(image_path)
        
        # 转换到HVI空间
        hvi_tensor = self.transform.HVIT(img_tensor)
        
        # 分离H、V、I通道
        H = hvi_tensor[0, 0, :, :].detach().cpu().numpy()
        V = hvi_tensor[0, 1, :, :].detach().cpu().numpy()
        I = hvi_tensor[0, 2, :, :].detach().cpu().numpy()
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('HVI色彩空间分解', fontsize=16)
        
        # 原始图像
        axes[0, 0].imshow(original_img)
        axes[0, 0].set_title('原始RGB图像')
        axes[0, 0].axis('off')
        
        # H通道 (色调相关)
        im1 = axes[0, 1].imshow(H, cmap='RdBu', vmin=-1, vmax=1)
        axes[0, 1].set_title('H通道 (色调)')
        axes[0, 1].axis('off')
        plt.colorbar(im1, ax=axes[0, 1], fraction=0.046)
        
        # V通道 (饱和度相关)
        im2 = axes[1, 0].imshow(V, cmap='RdBu', vmin=-1, vmax=1)
        axes[1, 0].set_title('V通道 (饱和度)')
        axes[1, 0].axis('off')
        plt.colorbar(im2, ax=axes[1, 0], fraction=0.046)
        
        # I通道 (强度)
        im3 = axes[1, 1].imshow(I, cmap='gray', vmin=0, vmax=1)
        axes[1, 1].set_title('I通道 (强度)')
        axes[1, 1].axis('off')
        plt.colorbar(im3, ax=axes[1, 1], fraction=0.046)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"HVI分解图已保存到: {save_path}")
        
        plt.show()
        
        return H, V, I
    
    def compare_rgb_hvi(self, image_path, save_path=None):
        """对比RGB和HVI表示的视觉效果"""
        # 加载图像
        img_tensor, original_img = self.load_image(image_path)
        
        # 转换到HVI空间再转回RGB
        hvi_tensor = self.transform.HVIT(img_tensor)
        reconstructed_rgb = self.transform.PHVIT(hvi_tensor)
        
        # 转换为PIL图像
        reconstructed_img = transforms.ToPILImage()(reconstructed_rgb[0].squeeze(0))
        
        # 创建对比图
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        fig.suptitle('RGB与HVI色彩空间对比', fontsize=16)
        
        # 原始RGB
        axes[0].imshow(original_img)
        axes[0].set_title('原始RGB图像')
        axes[0].axis('off')
        
        # HVI空间表示 (显示为伪彩色)
        hvi_vis = hvi_tensor[0].detach().cpu().numpy()
        hvi_vis[0] = (hvi_vis[0] + 1) / 2  # 归一化H通道到[0,1]
        hvi_vis[1] = (hvi_vis[1] + 1) / 2  # 归一化V通道到[0,1]
        axes[1].imshow(np.transpose(hvi_vis, (1, 2, 0)))
        axes[1].set_title('HVI空间表示 (伪彩色)')
        axes[1].axis('off')
        
        # 重建的RGB
        axes[2].imshow(reconstructed_img)
        axes[2].set_title('HVI重建的RGB图像')
        axes[2].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"RGB-HVI对比图已保存到: {save_path}")
        
        plt.show()
        
        return original_img, reconstructed_img
    
    def analyze_hvi_statistics(self, image_path):
        """分析HVI空间的统计特性"""
        img_tensor, _ = self.load_image(image_path)
        hvi_tensor = self.transform.HVIT(img_tensor)
        
        H = hvi_tensor[0, 0, :, :].detach().cpu().numpy()
        V = hvi_tensor[0, 1, :, :].detach().cpu().numpy()
        I = hvi_tensor[0, 2, :, :].detach().cpu().numpy()
        
        print("=== HVI色彩空间统计分析 ===")
        print(f"H通道: 均值={H.mean():.4f}, 标准差={H.std():.4f}, 范围=[{H.min():.4f}, {H.max():.4f}]")
        print(f"V通道: 均值={V.mean():.4f}, 标准差={V.std():.4f}, 范围=[{V.min():.4f}, {V.max():.4f}]")
        print(f"I通道: 均值={I.mean():.4f}, 标准差={I.std():.4f}, 范围=[{I.min():.4f}, {I.max():.4f}]")
        
        # 绘制直方图
        fig, axes = plt.subplots(1, 3, figsize=(15, 4))
        fig.suptitle('HVI通道分布直方图', fontsize=16)
        
        axes[0].hist(H.flatten(), bins=50, alpha=0.7, color='red')
        axes[0].set_title('H通道分布')
        axes[0].set_xlabel('H值')
        axes[0].set_ylabel('频次')
        
        axes[1].hist(V.flatten(), bins=50, alpha=0.7, color='green')
        axes[1].set_title('V通道分布')
        axes[1].set_xlabel('V值')
        axes[1].set_ylabel('频次')
        
        axes[2].hist(I.flatten(), bins=50, alpha=0.7, color='blue')
        axes[2].set_title('I通道分布')
        axes[2].set_xlabel('I值')
        axes[2].set_ylabel('频次')
        
        plt.tight_layout()
        plt.show()
    
    def demonstrate_color_sensitivity(self, image_path):
        """演示HVI的色彩敏感度特性"""
        img_tensor, original_img = self.load_image(image_path)
        
        # 不同的k值测试
        k_values = [0.5, 1.0, 2.0, 4.0]
        
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('不同k值下的HVI色彩敏感度', fontsize=16)
        
        # 原始图像
        axes[0, 0].imshow(original_img)
        axes[0, 0].set_title('原始RGB图像')
        axes[0, 0].axis('off')
        
        # 测试不同k值
        for i, k in enumerate(k_values):
            self.transform.density_k = k
            hvi_tensor = self.transform.HVIT(img_tensor)
            reconstructed_rgb = self.transform.PHVIT(hvi_tensor)
            reconstructed_img = transforms.ToPILImage()(reconstructed_rgb[0].squeeze(0))
            
            row = (i + 1) // 3
            col = (i + 1) % 3
            
            axes[row, col].imshow(reconstructed_img)
            axes[row, col].set_title(f'k={k}')
            axes[row, col].axis('off')
        
        # 隐藏多余的子图
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        plt.show()

def main():
    """主函数"""
    visualizer = HVIVisualizer()
    
    # 示例使用
    image_path = input("请输入图像路径: ").strip()
    
    if not os.path.exists(image_path):
        print(f"错误: 文件 {image_path} 不存在")
        return
    
    print("\n=== HVI色彩空间可视化工具 ===")
    print("1. 显示HVI三个通道的分离效果")
    print("2. 对比RGB和HVI表示")
    print("3. 分析HVI统计特性")
    print("4. 演示色彩敏感度")
    print("5. 运行所有分析")
    
    choice = input("\n请选择分析类型 (1-5): ").strip()
    
    if choice == '1':
        visualizer.visualize_hvi_components(image_path)
    elif choice == '2':
        visualizer.compare_rgb_hvi(image_path)
    elif choice == '3':
        visualizer.analyze_hvi_statistics(image_path)
    elif choice == '4':
        visualizer.demonstrate_color_sensitivity(image_path)
    elif choice == '5':
        visualizer.visualize_hvi_components(image_path)
        visualizer.compare_rgb_hvi(image_path)
        visualizer.analyze_hvi_statistics(image_path)
        visualizer.demonstrate_color_sensitivity(image_path)
    else:
        print("无效选择")

if __name__ == "__main__":
    main()