# HVI色彩空间可视化工具使用说明

## 目录结构
```
visualization/
├── visualize_hvi.py          # 基础HVI可视化工具
├── hvi_channels_demo.py      # HVI通道分离演示
├── rgb_hvi_comparison.py     # RGB-HVI对比工具
├── README.md                 # 使用说明 (本文件)
└── run_demo.sh               # 快速运行脚本
```

## 安装依赖
确保你的环境中安装了必要的Python包：
```bash
pip install torch torchvision numpy matplotlib pillow
```

## 使用方法

### 1. 基础HVI可视化工具 (`visualize_hvi.py`)

**功能**：
- HVI三个通道的分离显示
- RGB与HVI表示的对比
- HVI统计特性分析
- 色彩敏感度演示

**使用方法**：
```bash
cd visualization
python visualize_hvi.py
```

**交互选项**：
1. 显示HVI三个通道的分离效果
2. 对比RGB和HVI表示
3. 分析HVI统计特性
4. 演示色彩敏感度
5. 运行所有分析

### 2. HVI通道分离演示 (`hvi_channels_demo.py`)

**功能**：
- 理解HVI三个通道的物理含义
- 纯色测试，查看H、V、I通道的数值
- 真实图像的通道详细分析

**使用方法**：
```bash
cd visualization
python hvi_channels_demo.py
```

**交互选项**：
1. 理解HVI通道含义 (使用纯色测试)
2. 分析真实图像的HVI通道

### 3. RGB-HVI对比工具 (`rgb_hvi_comparison.py`)

**功能**：
- 对比RGB和HVI色彩空间的表示
- 演示HVI在低光环境下的优势
- 分析色彩分离能力
- 重建质量评估

**使用方法**：
```bash
cd visualization
python rgb_hvi_comparison.py
```

**交互选项**：
1. 对比真实图像的RGB和HVI表示
2. 演示HVI在低光环境下的优势
3. 分析HVI的色彩分离能力
4. 运行所有演示

## 快速运行

使用提供的快速运行脚本：
```bash
cd visualization
chmod +x run_demo.sh
./run_demo.sh
```

## HVI色彩空间说明

### 三个通道的含义：
- **H通道**: 色调的余弦分量，范围[-1, 1]
- **V通道**: 色调的正弦分量，范围[-1, 1]  
- **I通道**: 强度/亮度，范围[0, 1]

### HVI的优势：
1. **更好的色彩-亮度分离**: H和V通道主要包含色彩信息，I通道包含亮度信息
2. **低光增强友好**: 特别适合低光图像增强任务
3. **色彩敏感度调整**: 通过参数k可以调整色彩敏感度
4. **可微分**: 支持端到端训练

### 参数说明：
- `density_k`: 色彩敏感度控制参数
- `alpha_s`: 饱和度缩放因子
- `alpha`: 整体强度缩放因子
- `gated`: 饱和度门控开关
- `gated2`: 整体强度门控开关

## 示例输出

运行工具后，你会看到：
- 原始RGB图像
- H、V、I三个通道的分离显示
- HVI空间的伪彩色表示
- 重建的RGB图像
- 各种统计图表和分析结果

## 注意事项

1. 确保在正确的Python环境中运行（包含HVI-CIDNet的依赖）
2. 输入图像应为RGB格式
3. 可视化结果会以matplotlib窗口显示
4. 可以通过修改脚本中的参数来调整可视化效果

## 故障排除

如果遇到导入错误：
```bash
# 确保在项目根目录下运行
cd /home/<USER>/Projects/HVI-CIDNet

# 激活正确的conda环境
conda activate HVI

# 进入visualization目录
cd visualization

# 运行脚本
python visualize_hvi.py
```

如果matplotlib无法显示：
```bash
# 在服务器环境中，可以使用Agg后端
export MPLBACKEND=Agg
# 然后保存图像而不是显示
```