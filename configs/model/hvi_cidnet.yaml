# HVI-CIDNet 模型配置文件
# HVI-CIDNet: 基于HVI颜色空间的低光图像增强网络

type: HVI_CIDNet                # 模型类型标识符，对应@register_model装饰器

# ==================== 网络架构参数 ====================
architecture:
  # 基础通道配置
  input_channels: 3             # 输入图像通道数（RGB）
  output_channels: 3            # 输出图像通道数（RGB）
  base_channels: 64             # 基础特征通道数，控制网络容量
                                # 推荐值：32(轻量), 64(标准), 128(高容量)

  # 多尺度特征提取配置
  num_scales: 4                 # 多尺度特征提取的尺度数量
                                # 对应卷积核尺寸：[1, 3, 5, 7]
                                # 更多尺度可以捕获更丰富的特征，但增加计算量

  # 特征金字塔配置
  num_pyramid_levels: 3         # 特征金字塔的层数
                                # 推荐值：2-4，更多层次提供更好的多尺度融合

  # HVI颜色空间配置
  use_hvi_loss: true            # 是否在HVI空间计算额外的损失
                                # 有助于提高颜色恢复的质量

# ==================== 组件详细配置 ====================
components:
  # HVI颜色空间转换器配置
  hvi_transform:
    eps: 1.0e-8                 # 数值稳定性常数，防止除零错误
    
  # 输入编码器配置
  input_encoder:
    kernel_sizes: [7, 3, 3]     # 各层卷积核大小
    use_batch_norm: true        # 是否使用批归一化
    activation: "relu"          # 激活函数类型
    
  # 多尺度特征提取器配置
  multiscale_extractor:
    scales: [1, 3, 5, 7]        # 多尺度卷积核大小列表
    include_global: true        # 是否包含全局池化分支
    
  # 特征金字塔网络配置
  feature_pyramid:
    levels: ${model.architecture.num_pyramid_levels}  # 引用上面的配置
    use_lateral_conv: true      # 是否使用横向连接卷积
    
  # 颜色强度解耦器配置
  color_intensity_decoder:
    hidden_channels: ${model.architecture.base_channels}  # 隐藏层通道数
    num_residual_blocks: 4      # 每个分支的残差块数量
    use_depthwise_conv: true    # 是否使用深度可分离卷积
    
    # 颜色分支特定配置
    color_branch:
      focus_on_hue: true        # 是否特别关注色调恢复
      saturation_enhance: 1.2   # 饱和度增强系数
      
    # 强度分支特定配置
    intensity_branch:
      brightness_enhance: true  # 是否启用亮度增强
      contrast_enhance: true    # 是否启用对比度增强
      noise_suppress: true      # 是否启用噪声抑制
      
  # 轻量级交叉注意力配置
  cross_attention:
    reduction_ratio: 16         # 通道注意力的压缩比例
    spatial_kernel_size: 7      # 空间注意力的卷积核大小
    dropout_rate: 0.1           # Dropout比例
    
  # 输出层配置
  output_layer:
    use_residual: true          # 是否使用残差连接
    residual_weight: 0.1        # 残差连接权重
    output_activation: "sigmoid" # 输出激活函数

# ==================== 训练相关配置 ====================
training:
  # 损失函数权重配置
  loss_weights:
    rgb_loss: 1.0               # RGB空间损失权重
    hvi_loss: 0.5               # HVI空间损失权重（如果启用）
    perceptual_loss: 0.1        # 感知损失权重
    
  # 数据增强配置
  augmentation:
    use_color_jitter: true      # 是否使用颜色抖动
    use_geometric_transform: true # 是否使用几何变换
    hvi_space_augment: false    # 是否在HVI空间进行增强
    
  # 优化器相关
  optimizer_config:
    different_lr_for_branches: false  # 是否为不同分支使用不同学习率
    color_branch_lr_mult: 1.0   # 颜色分支学习率倍数
    intensity_branch_lr_mult: 1.0 # 强度分支学习率倍数

# ==================== 推理配置 ====================
inference:
  # 输出选项
  save_intermediate: false     # 是否保存中间结果
  save_hvi_representation: false # 是否保存HVI表示
  save_feature_maps: false     # 是否保存特征图
  
  # 后处理选项
  post_processing:
    gamma_correction: false     # 是否应用伽马校正
    gamma_value: 1.0           # 伽马值
    histogram_equalization: false # 是否应用直方图均衡化
    
  # 批处理配置
  batch_processing:
    enable: true               # 是否启用批处理
    max_batch_size: 4          # 最大批处理大小
    overlap_ratio: 0.1         # 重叠比例（用于大图像分块处理）

# ==================== 模型变体配置 ====================
# 可以通过修改这些配置创建不同的模型变体
variants:
  # 轻量级版本
  lightweight:
    base_channels: 32
    num_pyramid_levels: 2
    num_scales: 3
    
  # 高性能版本
  high_performance:
    base_channels: 128
    num_pyramid_levels: 4
    num_scales: 5
    
  # 移动端版本
  mobile:
    base_channels: 24
    num_pyramid_levels: 2
    num_scales: 2
    use_depthwise_conv: true

# ==================== 实验配置 ====================
experiments:
  # 消融实验配置
  ablation:
    disable_hvi_transform: false    # 禁用HVI转换（直接在RGB空间处理）
    disable_cross_attention: false # 禁用交叉注意力
    disable_multiscale: false      # 禁用多尺度特征提取
    disable_pyramid: false         # 禁用特征金字塔
    single_branch_only: false      # 仅使用单分支（颜色或强度）
    
  # 架构搜索配置
  nas:
    search_base_channels: [32, 48, 64, 80, 96]  # 搜索的基础通道数
    search_pyramid_levels: [2, 3, 4]            # 搜索的金字塔层数
    search_scales: [[1,3], [1,3,5], [1,3,5,7]]  # 搜索的多尺度配置

# ==================== 预训练模型 ====================
pretrained: null                 # 预训练模型路径
                                 # 示例："/path/to/pretrained_hvi_cidnet.pth"

# ==================== 模型初始化 ====================
init:
  type: "xavier_uniform"         # 初始化方法：normal/xavier_uniform/kaiming_normal
  gain: 1.0                      # 初始化增益
  
  # 特殊初始化配置
  special_init:
    residual_weight: 0.1         # 残差权重的初始值
    attention_weights: "small"   # 注意力权重初始化：small/normal/large

# ==================== 模型分析配置 ====================
analysis:
  # 复杂度分析
  compute_flops: true            # 是否计算FLOPs
  compute_params: true           # 是否计算参数量
  compute_memory: true           # 是否计算内存使用
  
  # 特征分析
  feature_visualization: false   # 是否启用特征可视化
  attention_visualization: false # 是否可视化注意力图
  
  # 性能分析
  profile_inference_time: false  # 是否分析推理时间
  profile_memory_usage: false    # 是否分析内存使用

# ==================== 兼容性配置 ====================
compatibility:
  # 与其他模型的兼容性
  support_onnx_export: true      # 是否支持ONNX导出
  support_tensorrt: false        # 是否支持TensorRT优化
  support_mobile_deployment: true # 是否支持移动端部署
  
  # 输入格式兼容性
  support_different_resolutions: true  # 是否支持不同分辨率
  min_resolution: [64, 64]       # 最小支持分辨率
  max_resolution: [2048, 2048]   # 最大支持分辨率
